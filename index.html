<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YOP MCP：一行提示词，AI 驱动您的易宝支付极速集成！</title>

    <!-- Tailwind CSS via CDN with fallback -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Fallback CSS if Tai<PERSON><PERSON> fails to load
        if (!window.tailwind) {
            const fallbackCSS = `
                * { box-sizing: border-box; margin: 0; padding: 0; }
                body { font-family: 'Inter', system-ui, -apple-system, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
                .btn { display: inline-block; padding: 12px 24px; border-radius: 8px; text-decoration: none; font-weight: 600; transition: all 0.3s; }
                .btn-primary { background: #2563EB; color: white; }
                .btn-primary:hover { background: #1D4ED8; }
                .btn-secondary { background: transparent; color: white; border: 2px solid white; }
                .btn-secondary:hover { background: white; color: #2563EB; }
                .hero { background: linear-gradient(135deg, #0EA5E9 0%, #2563EB 100%); color: white; padding: 100px 0; }
                .card { background: white; padding: 24px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 24px; }
                .grid { display: grid; gap: 24px; }
                .grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
                .grid-4 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
                .text-center { text-align: center; }
                .mb-4 { margin-bottom: 16px; }
                .mb-8 { margin-bottom: 32px; }
                .py-20 { padding: 80px 0; }
                .bg-gray-50 { background: #F9FAFB; }
                .bg-white { background: white; }
                h1 { font-size: 3rem; font-weight: 700; margin-bottom: 24px; }
                h2 { font-size: 2rem; font-weight: 600; margin-bottom: 16px; }
                h3 { font-size: 1.5rem; font-weight: 600; margin-bottom: 12px; }
                p { margin-bottom: 16px; }
                nav { background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 16px 0; position: fixed; top: 0; width: 100%; z-index: 1000; }
                .nav-content { display: flex; justify-content: space-between; align-items: center; }
                .nav-links { display: flex; gap: 24px; }
                .nav-links a { text-decoration: none; color: #666; font-weight: 500; }
                .nav-links a:hover { color: #2563EB; }
                footer { background: #1F2937; color: #D1D5DB; padding: 48px 0; }
                @media (max-width: 768px) {
                    h1 { font-size: 2rem; }
                    .grid-2, .grid-4 { grid-template-columns: 1fr; }
                    .nav-links { display: none; }
                }
            `;
            const style = document.createElement('style');
            style.textContent = fallbackCSS;
            document.head.appendChild(style);
        }
    </script>

    <!-- Font Awesome via CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- 自定义样式 -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #0EA5E9 0%, #2563EB 100%);
        }

        .hero-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .fade-in-up {
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hover-scale {
            transition: transform 0.3s ease;
        }

        .hover-scale:hover {
            transform: scale(1.05);
        }

        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: box-shadow 0.3s ease;
        }

        .card-shadow:hover {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .typing-animation {
            overflow: hidden;
            border-right: 2px solid #2563EB;
            white-space: nowrap;
            animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
        }

        @keyframes typing {
            from { width: 0; }
            to { width: 100%; }
        }

        @keyframes blink-caret {
            from, to { border-color: transparent; }
            50% { border-color: #2563EB; }
        }

        /* 增强的视觉效果 */
        .copy-button {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .bg-gray-800:hover .copy-button,
        .bg-gray-900:hover .copy-button {
            opacity: 1;
        }

        /* 改进的卡片悬停效果 */
        .card-shadow {
            transform: translateY(0);
            transition: all 0.3s ease;
        }

        .card-shadow:hover {
            transform: translateY(-4px);
        }

        /* 导航栏滚动效果 */
        .nav-scrolled {
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
        }

        /* 响应式改进 */
        @media (max-width: 768px) {
            .hero-pattern {
                background-size: 30px 30px;
            }

            .floating-animation {
                animation-duration: 4s;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-code text-white text-lg"></i>
                        </div>
                        <a href="#" class="text-xl font-bold text-gray-900">
                            YOP MCP
                        </a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="#features" class="text-gray-600 hover:text-gray-900 transition-colors">主要特性</a>
                    <a href="#prerequisites" class="text-gray-600 hover:text-gray-900 transition-colors">准备工作</a>
                    <a href="#quick-start" class="text-gray-600 hover:text-gray-900 transition-colors">快速开始</a>
                    <a href="https://github.com/yop-platform/yop-mcp-sdk" target="_blank"
                       class="text-gray-500 hover:text-gray-900 transition-colors">
                        <i class="fab fa-github text-xl"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-bg hero-pattern pt-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="lg:flex lg:items-center lg:justify-between">
                <div class="lg:w-1/2">
                    <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                        YOP MCP 来了！<br>
                        AI 赋能，一行提示词接入易宝支付。
                    </h1>
                    <p class="text-xl text-blue-100 mb-8 leading-relaxed">
                        专为开发者打造的全新集成体验，将数天的对接工作缩短至小时级，甚至分钟级。
                        告别繁琐，拥抱智能，让AI成为您的易宝支付集成伙伴！
                    </p>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="#quick-start"
                           class="inline-flex items-center justify-center px-8 py-3 text-base font-medium rounded-lg text-blue-600 bg-white hover:bg-blue-50 transition-colors duration-200">
                            <i class="fas fa-rocket mr-2"></i>
                            立即开始配置 YOP MCP
                        </a>
                        <a href="#demo-video"
                           class="inline-flex items-center justify-center px-8 py-3 text-base font-medium rounded-lg text-white border-2 border-white hover:bg-white hover:text-blue-600 transition-colors duration-200">
                            <i class="fas fa-play-circle mr-2"></i>
                            观看演示视频
                        </a>
                    </div>
                </div>
                <div class="lg:w-1/2 mt-10 lg:mt-0">
                    <div class="relative floating-animation">
                        <div class="relative rounded-lg shadow-xl bg-white p-6 card-shadow">
                            <div class="flex items-center mb-4">
                                <div class="flex space-x-2">
                                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                </div>
                                <div class="ml-auto text-xs text-gray-500">AI 编程助手</div>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-start fade-in-up">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                                            <i class="fas fa-user text-gray-600"></i>
                                        </div>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <p class="text-sm text-gray-700 bg-gray-100 rounded-lg p-3 shadow-sm">
                                            请帮我实现易宝支付的订单支付功能
                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-start fade-in-up" style="animation-delay: 0.5s;">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                                            <i class="fas fa-robot text-blue-600"></i>
                                        </div>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <div class="text-sm text-gray-700 bg-blue-50 rounded-lg p-3 shadow-sm">
                                            <p class="mb-2 flex items-center">
                                                <i class="fas fa-spinner fa-spin mr-2 text-blue-600"></i>
                                                正在通过YOP MCP分析最佳实现方案...
                                            </p>
                                            <div class="bg-gray-800 rounded-lg p-3 mt-2">
                                                <div class="flex items-center mb-2">
                                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                                                    <span class="text-green-400 text-xs">Python代码生成中...</span>
                                                </div>
                                                <code class="block text-green-400 text-xs leading-relaxed">
from yop import YopClient

client = YopClient("your_app_key")
response = client.pay.create({
    "order_id": "12345",
    "amount": 100.00,
    "currency": "CNY"
})
                                                </code>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 添加装饰性元素 -->
                            <div class="absolute -top-4 -right-4 w-8 h-8 bg-blue-500 rounded-full opacity-20"></div>
                            <div class="absolute -bottom-2 -left-2 w-6 h-6 bg-purple-500 rounded-full opacity-20"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-white" id="features">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 text-center mb-8">
                YOP MCP能为您做什么？
            </h2>

            <!-- 核心价值主张说明 -->
            <div class="max-w-4xl mx-auto text-center mb-16">
                <p class="text-lg text-gray-600 leading-relaxed mb-6">
                    YOP MCP (模型上下文协议) 是易宝支付为开发者精心打造的一项创新技术与工具集。它允许您通过您最喜爱的大型语言模型 (LLM)——无论它集成在您的IDE、AI编程工具中，还是您通过API调用的模型——以自然语言对话的方式，直接与易宝支付开放平台 (YOP) 的海量API进行交互和集成。
                </p>
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100">
                    <div class="flex items-center justify-center mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-lightbulb text-blue-600 text-xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900">释放 AI 的力量，革命化您的支付集成</h3>
                    </div>
                </div>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Feature 1 -->
                <div class="bg-white p-6 rounded-lg card-shadow hover-scale border border-gray-100 group">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors">
                        <i class="fas fa-bolt text-blue-600 text-xl group-hover:scale-110 transition-transform"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">极速高效</h3>
                    <p class="text-gray-600 mb-4">
                        从需求分析、API文档查询、SDK代码生成到关键配置辅助，AI全程加速，将传统模式下数天甚至数周的支付集成工作，显著缩短至小时乃至分钟级别。
                    </p>
                    <div class="space-y-2">
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>自动化需求分析</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>智能代码生成</span>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-blue-600 text-sm font-medium">
                        <span>数天 → 小时级</span>
                        <i class="fas fa-arrow-right ml-2"></i>
                    </div>
                </div>

                <!-- Feature 2 -->
                <div class="bg-white p-6 rounded-lg card-shadow hover-scale border border-gray-100 group">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-green-200 transition-colors">
                        <i class="fas fa-magic text-green-600 text-xl group-hover:scale-110 transition-transform"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">简单易用</h3>
                    <p class="text-gray-600 mb-4">
                        告别繁复的API文档检索和SDK细节的死记硬背。用您最熟悉的自然语言提问和下达指令，即可驱动复杂的API对接任务。
                    </p>
                    <div class="space-y-2">
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>自然语言交互</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>无需记忆API细节</span>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-green-600 text-sm font-medium">
                        <span>自然语言驱动</span>
                        <i class="fas fa-comments ml-2"></i>
                    </div>
                </div>

                <!-- Feature 3 -->
                <div class="bg-white p-6 rounded-lg card-shadow hover-scale border border-gray-100 group">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-purple-200 transition-colors">
                        <i class="fas fa-brain text-purple-600 text-xl group-hover:scale-110 transition-transform"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">智能强大</h3>
                    <p class="text-gray-600 mb-4">
                        AI不仅能深刻理解您的集成意图，更能通过YOP MCP在您本地安全地调用专属的 `yop-mcp` 命令行工具集。结合实时更新的YOP官方API文档与最佳实践知识库，AI能为您提供精准、可靠的集成方案与高质量的示例代码。
                    </p>
                    <div class="space-y-2">
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>深度意图理解</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>最佳实践知识库</span>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-purple-600 text-sm font-medium">
                        <span>实时文档同步</span>
                        <i class="fas fa-sync-alt ml-2"></i>
                    </div>
                </div>

                <!-- Feature 4 -->
                <div class="bg-white p-6 rounded-lg card-shadow hover-scale border border-gray-100 group">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4 group-hover:bg-red-200 transition-colors">
                        <i class="fas fa-shield-alt text-red-600 text-xl group-hover:scale-110 transition-transform"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">安全可控</h3>
                    <p class="text-gray-600 mb-4">
                        核心工具 `yop-mcp` 在您的本地环境执行，敏感信息（如密钥）的生成与存储均在本地完成，确保您的数据安全。其交互过程设计为通过标准输入输出进行，进一步增强了控制性。
                    </p>
                    <div class="space-y-2">
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>本地密钥生成</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>标准输入输出交互</span>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-red-600 text-sm font-medium">
                        <span>本地执行</span>
                        <i class="fas fa-lock ml-2"></i>
                    </div>
                </div>
            </div>

            <!-- YOP MCP 工作流程图 -->
            <div class="mt-20">
                <div class="text-center mb-12">
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">YOP MCP 工作原理</h3>
                    <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                        了解 YOP MCP 如何在您的开发环境中发挥桥梁作用，连接AI助手与易宝支付开放平台
                    </p>
                </div>

                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 border border-blue-100">
                    <div class="flex flex-col lg:flex-row items-center justify-between space-y-8 lg:space-y-0 lg:space-x-6">
                        <!-- 步骤1：开发者 + IDE -->
                        <div class="flex flex-col items-center text-center group">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors">
                                <i class="fas fa-user-cog text-blue-600 text-2xl"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">开发者 + IDE</h4>
                            <p class="text-sm text-gray-600">在您熟悉的开发环境中</p>
                        </div>

                        <!-- 箭头 -->
                        <div class="flex items-center">
                            <i class="fas fa-arrow-right text-gray-400 text-xl lg:block hidden"></i>
                            <i class="fas fa-arrow-down text-gray-400 text-xl lg:hidden block"></i>
                        </div>

                        <!-- 步骤2：自然语言提示 -->
                        <div class="flex flex-col items-center text-center group">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-green-200 transition-colors">
                                <i class="fas fa-comments text-green-600 text-2xl"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">自然语言提示</h4>
                            <p class="text-sm text-gray-600">用人类语言描述需求</p>
                        </div>

                        <!-- 箭头 -->
                        <div class="flex items-center">
                            <i class="fas fa-arrow-right text-gray-400 text-xl lg:block hidden"></i>
                            <i class="fas fa-arrow-down text-gray-400 text-xl lg:hidden block"></i>
                        </div>

                        <!-- 步骤3：AI大脑 -->
                        <div class="flex flex-col items-center text-center group">
                            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-purple-200 transition-colors">
                                <i class="fas fa-brain text-purple-600 text-2xl"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">AI 智能分析</h4>
                            <p class="text-sm text-gray-600">理解意图并制定方案</p>
                        </div>

                        <!-- 箭头 -->
                        <div class="flex items-center">
                            <i class="fas fa-arrow-right text-gray-400 text-xl lg:block hidden"></i>
                            <i class="fas fa-arrow-down text-gray-400 text-xl lg:hidden block"></i>
                        </div>

                        <!-- 步骤4：YOP MCP -->
                        <div class="flex flex-col items-center text-center group">
                            <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-orange-200 transition-colors">
                                <i class="fas fa-cogs text-orange-600 text-2xl"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">YOP MCP</h4>
                            <p class="text-sm text-gray-600">本地工具集执行</p>
                        </div>

                        <!-- 箭头 -->
                        <div class="flex items-center">
                            <i class="fas fa-arrow-right text-gray-400 text-xl lg:block hidden"></i>
                            <i class="fas fa-arrow-down text-gray-400 text-xl lg:hidden block"></i>
                        </div>

                        <!-- 步骤5：YOP平台 -->
                        <div class="flex flex-col items-center text-center group">
                            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4 group-hover:bg-red-200 transition-colors">
                                <i class="fas fa-cloud text-red-600 text-2xl"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 mb-2">YOP 开放平台</h4>
                            <p class="text-sm text-gray-600">获取最新API文档</p>
                        </div>
                    </div>

                    <!-- 详细说明 -->
                    <div class="mt-8 bg-white rounded-lg p-6 border border-gray-200">
                        <div class="grid md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-terminal text-blue-600"></i>
                                </div>
                                <h5 class="font-semibold text-gray-900 mb-2">标准输入输出</h5>
                                <p class="text-sm text-gray-600">通过 stdin/stdout 与 `uv yop-mcp` 进程安全通信</p>
                            </div>
                            <div class="text-center">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-shield-alt text-green-600"></i>
                                </div>
                                <h5 class="font-semibold text-gray-900 mb-2">本地执行</h5>
                                <p class="text-sm text-gray-600">所有敏感操作在您的本地环境中完成</p>
                            </div>
                            <div class="text-center">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-sync-alt text-purple-600"></i>
                                </div>
                                <h5 class="font-semibold text-gray-900 mb-2">实时同步</h5>
                                <p class="text-sm text-gray-600">获取最新的API文档和最佳实践</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Prerequisites Section -->
    <section class="py-20 bg-white" id="prerequisites">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- 页面标题 -->
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-gray-900 mb-4">准备工作</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    在开始使用 YOP MCP 之前，请确保您已具备以下条件。我们将引导您完成每一步的准备工作。
                </p>
            </div>

            <!-- 环境要求卡片网格 -->
            <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mb-12">
                <!-- AI编程环境 -->
                <div class="bg-white rounded-lg card-shadow hover-scale p-6 border border-gray-100 group">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                            <i class="fas fa-robot text-purple-600 text-2xl group-hover:scale-110 transition-transform"></i>
                        </div>
                        <h3 class="ml-4 text-xl font-semibold text-gray-900">AI编程环境</h3>
                    </div>
                    <div class="space-y-4">
                        <p class="text-gray-600">您需要具备以下任一环境：</p>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-3 flex-shrink-0"></i>
                                <span class="text-gray-700">支持工具调用的IDE (如Cursor)</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-3 flex-shrink-0"></i>
                                <span class="text-gray-700">AI编程工具 (如Cline, RooCode)</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-3 flex-shrink-0"></i>
                                <span class="text-gray-700">通过API调用的LLM (如Claude)</span>
                            </li>
                        </ul>
                        <div class="mt-4 p-3 bg-purple-50 rounded-lg border-l-4 border-purple-500">
                            <p class="text-purple-700 text-sm">
                                <i class="fas fa-lightbulb mr-2"></i>
                                推荐使用支持MCP协议的现代AI开发工具
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Python环境 -->
                <div class="bg-white rounded-lg card-shadow hover-scale p-6 border border-gray-100 group">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                            <i class="fab fa-python text-blue-600 text-2xl group-hover:scale-110 transition-transform"></i>
                        </div>
                        <h3 class="ml-4 text-xl font-semibold text-gray-900">Python环境</h3>
                    </div>
                    <div class="space-y-4">
                        <p class="text-gray-600">确保您的系统已安装：</p>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-3 flex-shrink-0"></i>
                                <span class="text-gray-700">Python 3.8 或更高版本</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-3 flex-shrink-0"></i>
                                <span class="text-gray-700">包管理工具 uv (推荐) 或 pip</span>
                            </li>
                        </ul>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-terminal text-gray-600 mr-2"></i>
                                <span class="text-sm font-medium text-gray-700">快速检查</span>
                            </div>
                            <code class="text-sm text-gray-700 block">
# 检查Python版本<br>
python --version<br><br>
# 安装uv (推荐)<br>
curl -LsSf https://astral.sh/uv/install.sh | sh
                            </code>
                        </div>
                    </div>
                </div>

                <!-- 易宝支付商户信息 -->
                <div class="bg-white rounded-lg card-shadow hover-scale p-6 border border-gray-100 group">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
                            <i class="fas fa-id-card text-green-600 text-2xl group-hover:scale-110 transition-transform"></i>
                        </div>
                        <h3 class="ml-4 text-xl font-semibold text-gray-900">商户信息</h3>
                    </div>
                    <div class="space-y-4">
                        <p class="text-gray-600">实际API对接时需要：</p>
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-3 flex-shrink-0"></i>
                                <span class="text-gray-700">易宝支付商户编号 (merchantNo)</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-3 flex-shrink-0"></i>
                                <span class="text-gray-700">应用密钥对 (将由YOP MCP协助生成)</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-info-circle text-blue-500 mt-1 mr-3 flex-shrink-0"></i>
                                <span class="text-gray-700">测试时可使用沙箱环境数据</span>
                            </li>
                        </ul>
                        <div class="mt-4 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                            <p class="text-blue-700 text-sm">
                                <i class="fas fa-info-circle mr-2"></i>
                                沙箱环境提供模拟数据，方便您进行开发测试
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 准备就绪提示 -->
            <div class="text-center bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 border border-blue-100">
                <div class="max-w-2xl mx-auto">
                    <div class="flex justify-center mb-4">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-rocket text-blue-600 text-2xl"></i>
                        </div>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">准备就绪？让我们开始吧！</h3>
                    <p class="text-gray-600 mb-6">
                        所有准备工作完成后，您就可以开始配置 YOP MCP 并体验 AI 驱动的支付集成了。
                    </p>
                    <div class="flex flex-col sm:flex-row justify-center gap-4">
                        <a href="#quick-start"
                           class="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                            <i class="fas fa-arrow-down mr-2"></i>
                            开始配置
                        </a>
                        <a href="pages/quick-start.html"
                           class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                            <i class="fas fa-book mr-2"></i>
                            详细教程
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Start Section -->
    <section class="py-20 bg-gray-50" id="quick-start">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">
                只需两步，激活您的 AI 集成超能力！
            </h2>
            <div class="grid lg:grid-cols-2 gap-12">
                <!-- Step 1 -->
                <div class="group">
                    <div class="bg-white p-8 rounded-lg card-shadow hover-scale border border-gray-100">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg shadow-lg">
                                1
                            </div>
                            <h3 class="ml-4 text-xl font-semibold text-gray-900">安装 YOP MCP 命令行工具</h3>
                        </div>
                        <div class="mb-6">
                            <div class="bg-gray-900 rounded-lg p-4 relative overflow-hidden">
                                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-green-400 to-blue-500"></div>
                                <div class="flex items-center mb-3">
                                    <div class="flex space-x-2">
                                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    </div>
                                    <span class="ml-auto text-gray-400 text-xs">Terminal</span>
                                </div>
                                <code class="text-green-400 text-sm block leading-relaxed">
<span class="text-gray-500"># 创建虚拟环境</span>
python -m venv yop_mcp_env

<span class="text-gray-500"># 激活环境</span>
source yop_mcp_env/bin/activate  <span class="text-gray-500"># Linux/macOS</span>

<span class="text-gray-500"># 安装 yop-mcp</span>
uv pip install yop-mcp
                                </code>
                            </div>
                        </div>
                        <div class="flex items-start p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                            <i class="fas fa-info-circle text-blue-600 mt-1 mr-3"></i>
                            <p class="text-blue-800 text-sm">
                                确保您已安装 Python 3.8+ 和包管理工具 uv 或 pip
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="group">
                    <div class="bg-white p-8 rounded-lg card-shadow hover-scale border border-gray-100">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-full flex items-center justify-center font-bold text-lg shadow-lg">
                                2
                            </div>
                            <h3 class="ml-4 text-xl font-semibold text-gray-900">配置 AI 工具</h3>
                        </div>
                        <div class="mb-6">
                            <div class="bg-gray-900 rounded-lg p-4 relative overflow-hidden">
                                <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-purple-400 to-pink-500"></div>
                                <div class="flex items-center mb-3">
                                    <div class="flex space-x-2">
                                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    </div>
                                    <span class="ml-auto text-gray-400 text-xs">mcp_config.json</span>
                                </div>
                                <code class="text-green-400 text-sm block leading-relaxed">
{
  <span class="text-blue-400">"mcpServers"</span>: {
    <span class="text-blue-400">"mcpadvisor"</span>: {
      <span class="text-blue-400">"command"</span>: <span class="text-yellow-400">"uv"</span>,
      <span class="text-blue-400">"args"</span>: [
        <span class="text-yellow-400">"yop-mcp"</span>
      ]
    }
  }
}
                                </code>
                            </div>
                        </div>
                        <div class="flex items-start p-4 bg-purple-50 rounded-lg border-l-4 border-purple-500">
                            <i class="fas fa-cog text-purple-600 mt-1 mr-3"></i>
                            <p class="text-purple-800 text-sm">
                                在您的IDE或AI编程工具中添加上述MCP配置
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 下一步操作 -->
            <div class="mt-16 text-center">
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8 border border-blue-100">
                    <div class="max-w-2xl mx-auto">
                        <div class="flex justify-center mb-4">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-check-circle text-green-600 text-2xl"></i>
                            </div>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">配置完成！开始您的第一个集成</h3>
                        <p class="text-gray-600 mb-6">
                            恭喜！您已经成功配置了 YOP MCP。现在可以开始体验 AI 驱动的支付集成了。
                        </p>
                        <div class="flex flex-col sm:flex-row justify-center gap-4">
                            <a href="pages/first-integration.html"
                               class="inline-flex items-center justify-center px-8 py-3 text-lg font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl">
                                <i class="fas fa-code mr-2"></i>
                                开始第一个集成
                            </a>
                            <a href="pages/quick-start.html"
                               class="inline-flex items-center justify-center px-8 py-3 text-lg font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200 border border-gray-300">
                                <i class="fas fa-book mr-2"></i>
                                查看详细教程
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-gray-300 py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <div class="col-span-2">
                    <span class="text-xl font-bold text-white block mb-4">YOP MCP</span>
                    <p class="text-gray-400">
                        YOP MCP (模型上下文协议) 是易宝支付为开发者精心打造的创新技术与工具集，
                        让您通过AI助手轻松完成支付集成。
                    </p>
                </div>
                <div>
                    <h4 class="text-white font-semibold mb-4">资源</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white">开发者文档</a></li>
                        <li><a href="#" class="hover:text-white">API 文档</a></li>
                        <li><a href="#" class="hover:text-white">示例代码</a></li>
                        <li><a href="#" class="hover:text-white">常见问题</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-white font-semibold mb-4">联系我们</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="hover:text-white">技术支持</a></li>
                        <li><a href="#" class="hover:text-white">商务合作</a></li>
                        <li><a href="#" class="hover:text-white">加入我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>© 2025 易宝支付. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript for enhanced interactions -->
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add scroll-triggered animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.card-shadow, .floating-animation').forEach(el => {
            observer.observe(el);
        });

        // Add copy functionality to code blocks
        document.querySelectorAll('code').forEach(codeBlock => {
            const parent = codeBlock.parentElement;
            if (parent.tagName === 'DIV' && (parent.classList.contains('bg-gray-800') || parent.classList.contains('bg-gray-900'))) {
                // Check if copy button already exists
                if (parent.querySelector('.copy-button')) return;

                const copyButton = document.createElement('button');
                copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                copyButton.className = 'copy-button absolute top-2 right-2 text-gray-400 hover:text-white transition-colors p-2 rounded z-10';
                copyButton.style.cssText = 'position: absolute; top: 8px; right: 8px;';

                copyButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const text = codeBlock.textContent.trim();
                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        navigator.clipboard.writeText(text).then(() => {
                            copyButton.innerHTML = '<i class="fas fa-check"></i>';
                            copyButton.classList.add('text-green-400');
                            setTimeout(() => {
                                copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                                copyButton.classList.remove('text-green-400');
                            }, 2000);
                        }).catch(() => {
                            // Fallback for older browsers
                            fallbackCopyTextToClipboard(text, copyButton);
                        });
                    } else {
                        // Fallback for older browsers
                        fallbackCopyTextToClipboard(text, copyButton);
                    }
                });

                parent.style.position = 'relative';
                parent.appendChild(copyButton);
            }
        });

        // Fallback copy function for older browsers
        function fallbackCopyTextToClipboard(text, button) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            try {
                document.execCommand('copy');
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.classList.add('text-green-400');
                setTimeout(() => {
                    button.innerHTML = '<i class="fas fa-copy"></i>';
                    button.classList.remove('text-green-400');
                }, 2000);
            } catch (err) {
                console.error('复制失败:', err);
            }
            document.body.removeChild(textArea);
        }

        // Add typing effect to hero section (improved version)
        const heroTitle = document.querySelector('h1');
        if (heroTitle) {
            const text = heroTitle.textContent.trim();
            if (text) {
                heroTitle.textContent = '';
                heroTitle.style.borderRight = '2px solid white';
                heroTitle.style.minHeight = '1.2em'; // Prevent layout shift

                let i = 0;
                const typeWriter = () => {
                    if (i < text.length) {
                        heroTitle.textContent += text.charAt(i);
                        i++;
                        setTimeout(typeWriter, 30); // Faster typing speed
                    } else {
                        setTimeout(() => {
                            heroTitle.style.borderRight = 'none';
                        }, 1000);
                    }
                };

                // Start typing effect after page load
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', () => {
                        setTimeout(typeWriter, 500);
                    });
                } else {
                    setTimeout(typeWriter, 500);
                }
            }
        }

        // Add progress indicator for scroll
        const progressBar = document.createElement('div');
        progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #0EA5E9, #2563EB);
            z-index: 9999;
            transition: width 0.3s ease;
        `;
        document.body.appendChild(progressBar);

        window.addEventListener('scroll', () => {
            const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
            progressBar.style.width = scrolled + '%';

            // Add navigation bar scroll effect
            const nav = document.querySelector('nav');
            if (window.scrollY > 50) {
                nav.classList.add('nav-scrolled');
            } else {
                nav.classList.remove('nav-scrolled');
            }
        });

        // Initialize animations on page load
        document.addEventListener('DOMContentLoaded', () => {
            // Trigger fade-in animations for visible elements
            const animatedElements = document.querySelectorAll('.fade-in-up');
            animatedElements.forEach((el, index) => {
                setTimeout(() => {
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
